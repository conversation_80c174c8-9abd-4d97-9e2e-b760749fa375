# Copyright 2024 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from pytest import approx

huggingface_text_embedding_expected_output = [
    approx(0.09812460094690323, abs=1e-6),
    approx(0.06781268864870071, abs=1e-6),
    approx(0.06252312660217285, abs=1e-6),
    approx(0.09508480876684189, abs=1e-6),
    approx(0.03664757311344147, abs=1e-6),
    approx(-0.003984605893492699, abs=1e-6),
    approx(0.00747756939381361, abs=1e-6),
    approx(-0.013231473974883556, abs=1e-6),
    approx(0.06288368254899979, abs=1e-6),
    approx(0.022495508193969727, abs=1e-6),
    approx(0.07269583642482758, abs=1e-6),
    approx(-0.031274277716875076, abs=1e-6),
    approx(0.04635509476065636, abs=1e-6),
    approx(-0.012554511427879333, abs=1e-6),
    approx(0.047814756631851196, abs=1e-6),
    approx(-0.004910356365144253, abs=1e-6),
    approx(0.04941992461681366, abs=1e-6),
    approx(-0.06410923600196838, abs=1e-6),
    approx(-0.09696584939956665, abs=1e-6),
    approx(0.03288877382874489, abs=1e-6),
    approx(0.054104436188936234, abs=1e-6),
    approx(0.03532857820391655, abs=1e-6),
    approx(0.03305051475763321, abs=1e-6),
    approx(0.014699393883347511, abs=1e-6),
    approx(-0.033430635929107666, abs=1e-6),
    approx(-0.025615854188799858, abs=1e-6),
    approx(-0.050792139023542404, abs=1e-6),
    approx(0.0732545554637909, abs=1e-6),
    approx(0.11027403920888901, abs=1e-6),
    approx(-0.02966182678937912, abs=1e-6),
    approx(-0.06755708158016205, abs=1e-6),
    approx(-0.030571527779102325, abs=1e-6),
    approx(0.039560187608003616, abs=1e-6),
    approx(0.04547598585486412, abs=1e-6),
    approx(0.015996195375919342, abs=1e-6),
    approx(0.03855040669441223, abs=1e-6),
    approx(-0.010954046621918678, abs=1e-6),
    approx(0.08483565598726273, abs=1e-6),
    approx(-0.04428708180785179, abs=1e-6),
    approx(-0.0067963870242238045, abs=1e-6),
    approx(0.00942565780133009, abs=1e-6),
    approx(5.072941348771565e-05, abs=1e-6),
    approx(0.0013035445008426905, abs=1e-6),
    approx(-0.011969788931310177, abs=1e-6),
    approx(0.01364515908062458, abs=1e-6),
    approx(-0.08417423069477081, abs=1e-6),
    approx(-0.00016511740977875888, abs=1e-6),
    approx(0.005483777727931738, abs=1e-6),
    approx(0.025615010410547256, abs=1e-6),
    approx(-0.031545329838991165, abs=1e-6),
    approx(-0.10734471678733826, abs=1e-6),
    approx(-0.04578779265284538, abs=1e-6),
    approx(-0.09117498993873596, abs=1e-6),
    approx(-0.0025104954838752747, abs=1e-6),
    approx(0.017998425289988518, abs=1e-6),
    approx(0.04940157011151314, abs=1e-6),
    approx(0.006184814497828484, abs=1e-6),
    approx(0.05979632958769798, abs=1e-6),
    approx(0.027002627030014992, abs=1e-6),
    approx(-0.016122253611683846, abs=1e-6),
    approx(-0.01814969629049301, abs=1e-6),
    approx(-0.023634832352399826, abs=1e-6),
    approx(-0.09489716589450836, abs=1e-6),
    approx(0.06621630489826202, abs=1e-6),
    approx(0.14922751486301422, abs=1e-6),
    approx(0.024338768795132637, abs=1e-6),
    approx(0.001210211543366313, abs=1e-6),
    approx(0.006072015967220068, abs=1e-6),
    approx(-0.09917040914297104, abs=1e-6),
    approx(0.08505816012620926, abs=1e-6),
    approx(0.030826149508357048, abs=1e-6),
    approx(0.02918659895658493, abs=1e-6),
    approx(-0.0239559356123209, abs=1e-6),
    approx(-0.01055932603776455, abs=1e-6),
    approx(-0.0721520185470581, abs=1e-6),
    approx(-0.03525644540786743, abs=1e-6),
    approx(0.03334657475352287, abs=1e-6),
    approx(-0.043929483741521835, abs=1e-6),
    approx(0.11975517123937607, abs=1e-6),
    approx(0.08864746987819672, abs=1e-6),
    approx(-0.09591905772686005, abs=1e-6),
    approx(-0.05908012390136719, abs=1e-6),
    approx(-0.008292673155665398, abs=1e-6),
    approx(0.03816917911171913, abs=1e-6),
    approx(0.049799688160419464, abs=1e-6),
    approx(-0.0326821394264698, abs=1e-6),
    approx(0.019901027902960777, abs=1e-6),
    approx(-0.10837853699922562, abs=1e-6),
    approx(0.014679030515253544, abs=1e-6),
    approx(0.018117807805538177, abs=1e-6),
    approx(-0.061317842453718185, abs=1e-6),
    approx(-0.08975035697221756, abs=1e-6),
    approx(0.04922156780958176, abs=1e-6),
    approx(-0.0208709929138422, abs=1e-6),
    approx(0.004055118653923273, abs=1e-6),
    approx(-0.03543012961745262, abs=1e-6),
    approx(-0.05657775700092316, abs=1e-6),
    approx(-0.048313774168491364, abs=1e-6),
    approx(-0.024660272523760796, abs=1e-6),
    approx(0.07359181344509125, abs=1e-6),
    approx(-0.020923519507050514, abs=1e-6),
    approx(0.040697671473026276, abs=1e-6),
    approx(0.03232649713754654, abs=1e-6),
    approx(0.07668346911668777, abs=1e-6),
    approx(-0.014747696928679943, abs=1e-6),
    approx(-0.10304528474807739, abs=1e-6),
    approx(-0.04274493455886841, abs=1e-6),
    approx(-0.06629316508769989, abs=1e-6),
    approx(0.01547403447329998, abs=1e-6),
    approx(-0.02680026739835739, abs=1e-6),
    approx(-0.09175192564725876, abs=1e-6),
    approx(-0.03560047224164009, abs=1e-6),
    approx(-0.018932463601231575, abs=1e-6),
    approx(-0.004029626026749611, abs=1e-6),
    approx(-0.020193425938487053, abs=1e-6),
    approx(-0.05853483825922012, abs=1e-6),
    approx(-0.10180168598890305, abs=1e-6),
    approx(-0.0044878339394927025, abs=1e-6),
    approx(-0.06976301968097687, abs=1e-6),
    approx(0.06024095416069031, abs=1e-6),
    approx(0.02981085516512394, abs=1e-6),
    approx(0.06380829215049744, abs=1e-6),
    approx(-0.06730612367391586, abs=1e-6),
    approx(0.003350921906530857, abs=1e-6),
    approx(-0.06003981828689575, abs=1e-6),
    approx(-0.09313663095235825, abs=1e-6),
    approx(0.04567500948905945, abs=1e-6),
    approx(-6.719925780142724e-33, abs=1e-6),
    approx(0.0074332281947135925, abs=1e-6),
    approx(-0.0651097446680069, abs=1e-6),
    approx(-0.02125914767384529, abs=1e-6),
    approx(0.035196032375097275, abs=1e-6),
    approx(0.0463360995054245, abs=1e-6),
    approx(-0.07030492275953293, abs=1e-6),
    approx(-0.06128954142332077, abs=1e-6),
    approx(0.017366604879498482, abs=1e-6),
    approx(-0.007479745429009199, abs=1e-6),
    approx(-0.018468711525201797, abs=1e-6),
    approx(0.0016074671875685453, abs=1e-6),
    approx(-0.025801850482821465, abs=1e-6),
    approx(0.0300469808280468, abs=1e-6),
    approx(0.008751698769629002, abs=1e-6),
    approx(0.05092954635620117, abs=1e-6),
    approx(0.07171620428562164, abs=1e-6),
    approx(-0.07271797209978104, abs=1e-6),
    approx(0.13261504471302032, abs=1e-6),
    approx(-0.00803413987159729, abs=1e-6),
    approx(0.03693221136927605, abs=1e-6),
    approx(-0.023691324517130852, abs=1e-6),
    approx(0.024269407615065575, abs=1e-6),
    approx(-0.026396198198199272, abs=1e-6),
    approx(-0.018338488414883614, abs=1e-6),
    approx(-0.014753266237676144, abs=1e-6),
    approx(-0.0743173211812973, abs=1e-6),
    approx(0.016344882547855377, abs=1e-6),
    approx(0.0180369820445776, abs=1e-6),
    approx(-0.0031233245972543955, abs=1e-6),
    approx(0.04128265753388405, abs=1e-6),
    approx(0.05059865862131119, abs=1e-6),
    approx(0.04449484497308731, abs=1e-6),
    approx(-0.047552984207868576, abs=1e-6),
    approx(-0.002626449102535844, abs=1e-6),
    approx(0.054667823016643524, abs=1e-6),
    approx(0.04697798937559128, abs=1e-6),
    approx(0.06379824131727219, abs=1e-6),
    approx(-0.0218367762863636, abs=1e-6),
    approx(0.0011920174583792686, abs=1e-6),
    approx(0.00615497725084424, abs=1e-6),
    approx(-0.01047475729137659, abs=1e-6),
    approx(-0.051548853516578674, abs=1e-6),
    approx(0.021718978881835938, abs=1e-6),
    approx(-0.03787216544151306, abs=1e-6),
    approx(0.08791148662567139, abs=1e-6),
    approx(0.0236575435847044, abs=1e-6),
    approx(0.019885707646608353, abs=1e-6),
    approx(0.021743182092905045, abs=1e-6),
    approx(-0.06192108988761902, abs=1e-6),
    approx(0.020616136491298676, abs=1e-6),
    approx(-0.01068698987364769, abs=1e-6),
    approx(0.05776219815015793, abs=1e-6),
    approx(0.05940284579992294, abs=1e-6),
    approx(-0.028875550255179405, abs=1e-6),
    approx(0.06947717070579529, abs=1e-6),
    approx(0.0010136704659089446, abs=1e-6),
    approx(0.042007941752672195, abs=1e-6),
    approx(0.03475204482674599, abs=1e-6),
    approx(-0.0016648498130962253, abs=1e-6),
    approx(-0.010128081776201725, abs=1e-6),
    approx(-0.054719988256692886, abs=1e-6),
    approx(0.040564633905887604, abs=1e-6),
    approx(-0.056016333401203156, abs=1e-6),
    approx(0.05726315826177597, abs=1e-6),
    approx(-0.025760939344763756, abs=1e-6),
    approx(0.02939697913825512, abs=1e-6),
    approx(0.010298469103872776, abs=1e-6),
    approx(-0.049152445048093796, abs=1e-6),
    approx(0.015427765436470509, abs=1e-6),
    approx(0.028693322092294693, abs=1e-6),
    approx(0.011681453324854374, abs=1e-6),
    approx(-0.0069687520153820515, abs=1e-6),
    approx(-0.08346765488386154, abs=1e-6),
    approx(0.1083698645234108, abs=1e-6),
    approx(-0.04198264703154564, abs=1e-6),
    approx(-0.04266297444701195, abs=1e-6),
    approx(-0.021897386759519577, abs=1e-6),
    approx(-0.02162591740489006, abs=1e-6),
    approx(-0.007852147333323956, abs=1e-6),
    approx(0.0382203571498394, abs=1e-6),
    approx(-0.01800900511443615, abs=1e-6),
    approx(-0.14943552017211914, abs=1e-6),
    approx(0.023226426914334297, abs=1e-6),
    approx(0.015028528869152069, abs=1e-6),
    approx(-0.03989403694868088, abs=1e-6),
    approx(0.0037517233286052942, abs=1e-6),
    approx(0.04300670325756073, abs=1e-6),
    approx(-0.084754578769207, abs=1e-6),
    approx(-0.03029678948223591, abs=1e-6),
    approx(-0.020130692049860954, abs=1e-6),
    approx(-0.02503017708659172, abs=1e-6),
    approx(0.003525990294292569, abs=1e-6),
    approx(0.05823483690619469, abs=1e-6),
    approx(0.005494723096489906, abs=1e-6),
    approx(0.006489735096693039, abs=1e-6),
    approx(3.174683674546661e-33, abs=1e-6),
    approx(-0.042438242584466934, abs=1e-6),
    approx(0.02992217242717743, abs=1e-6),
    approx(-0.08836193382740021, abs=1e-6),
    approx(0.06575305014848709, abs=1e-6),
    approx(0.10463074594736099, abs=1e-6),
    approx(-0.008896651677787304, abs=1e-6),
    approx(0.04289451241493225, abs=1e-6),
    approx(-0.09550552070140839, abs=1e-6),
    approx(0.03168182820081711, abs=1e-6),
    approx(0.07352748513221741, abs=1e-6),
    approx(-0.13084882497787476, abs=1e-6),
    approx(0.02552989311516285, abs=1e-6),
    approx(0.016062133014202118, abs=1e-6),
    approx(-0.004375615157186985, abs=1e-6),
    approx(0.02920258417725563, abs=1e-6),
    approx(-0.008134118281304836, abs=1e-6),
    approx(0.06525745987892151, abs=1e-6),
    approx(0.0006216352921910584, abs=1e-6),
    approx(-0.007835793308913708, abs=1e-6),
    approx(0.041560620069503784, abs=1e-6),
    approx(-0.04372050613164902, abs=1e-6),
    approx(0.09504030644893646, abs=1e-6),
    approx(-0.025842614471912384, abs=1e-6),
    approx(0.08701658993959427, abs=1e-6),
    approx(-0.04147149249911308, abs=1e-6),
    approx(0.03544051572680473, abs=1e-6),
    approx(-0.014387136325240135, abs=1e-6),
    approx(-0.026075268164277077, abs=1e-6),
    approx(-0.09590042382478714, abs=1e-6),
    approx(-0.008881512098014355, abs=1e-6),
    approx(0.007069997023791075, abs=1e-6),
    approx(-0.09052383154630661, abs=1e-6),
    approx(-0.0797419622540474, abs=1e-6),
    approx(-0.014227600768208504, abs=1e-6),
    approx(-0.05348990857601166, abs=1e-6),
    approx(0.05122567340731621, abs=1e-6),
    approx(0.024540811777114868, abs=1e-6),
    approx(-0.03697468340396881, abs=1e-6),
    approx(-0.0734110027551651, abs=1e-6),
    approx(-0.006874291691929102, abs=1e-6),
    approx(0.0068125007674098015, abs=1e-6),
    approx(-0.03291832655668259, abs=1e-6),
    approx(0.015790732577443123, abs=1e-6),
    approx(0.10594560205936432, abs=1e-6),
    approx(0.022715117782354355, abs=1e-6),
    approx(-0.05641733109951019, abs=1e-6),
    approx(-0.032064203172922134, abs=1e-6),
    approx(-0.10509152710437775, abs=1e-6),
    approx(-0.03290463984012604, abs=1e-6),
    approx(0.05139185115695, abs=1e-6),
    approx(-0.10034648329019547, abs=1e-6),
    approx(0.044995229691267014, abs=1e-6),
    approx(-0.03296136111021042, abs=1e-6),
    approx(-0.019640808925032616, abs=1e-6),
    approx(-0.10540889203548431, abs=1e-6),
    approx(0.011973694898188114, abs=1e-6),
    approx(-0.011170921847224236, abs=1e-6),
    approx(-0.06957732141017914, abs=1e-6),
    approx(-0.009789649397134781, abs=1e-6),
    approx(0.016010915860533714, abs=1e-6),
    approx(-0.05117954686284065, abs=1e-6),
    approx(0.008791357278823853, abs=1e-6),
    approx(-0.006576159968972206, abs=1e-6),
    approx(0.0662924274802208, abs=1e-6),
    approx(0.10956111550331116, abs=1e-6),
    approx(-0.054759278893470764, abs=1e-6),
    approx(-0.05709334462881088, abs=1e-6),
    approx(0.05029774457216263, abs=1e-6),
    approx(0.012622491456568241, abs=1e-6),
    approx(0.02845408022403717, abs=1e-6),
    approx(0.1343768686056137, abs=1e-6),
    approx(0.011013726703822613, abs=1e-6),
    approx(-0.15195167064666748, abs=1e-6),
    approx(-0.04947613552212715, abs=1e-6),
    approx(-0.06537330150604248, abs=1e-6),
    approx(-0.06709233671426773, abs=1e-6),
    approx(-0.06969749927520752, abs=1e-6),
    approx(-0.03129565715789795, abs=1e-6),
    approx(-0.06176615506410599, abs=1e-6),
    approx(-0.07420394569635391, abs=1e-6),
    approx(0.028973843902349472, abs=1e-6),
    approx(-0.008312417194247246, abs=1e-6),
    approx(0.04321927949786186, abs=1e-6),
    approx(-0.017557447776198387, abs=1e-6),
    approx(-0.0051787244156003, abs=1e-6),
    approx(-0.05432431399822235, abs=1e-6),
    approx(-0.04127557948231697, abs=1e-6),
    approx(-0.08270694315433502, abs=1e-6),
    approx(-0.015986457467079163, abs=1e-6),
    approx(0.008617997169494629, abs=1e-6),
    approx(0.0006256008055061102, abs=1e-6),
    approx(-0.06523493677377701, abs=1e-6),
    approx(0.07926346361637115, abs=1e-6),
    approx(0.02680228278040886, abs=1e-6),
    approx(-0.025414032861590385, abs=1e-6),
    approx(-1.965175044915668e-08, abs=1e-6),
    approx(-0.022532761096954346, abs=1e-6),
    approx(-0.015100622549653053, abs=1e-6),
    approx(0.08194082975387573, abs=1e-6),
    approx(-0.03959039971232414, abs=1e-6),
    approx(0.04711082577705383, abs=1e-6),
    approx(0.015206306241452694, abs=1e-6),
    approx(0.06537686288356781, abs=1e-6),
    approx(0.01843535713851452, abs=1e-6),
    approx(-0.007971754297614098, abs=1e-6),
    approx(-0.03773094713687897, abs=1e-6),
    approx(0.023044036701321602, abs=1e-6),
    approx(0.04045030474662781, abs=1e-6),
    approx(-0.056972041726112366, abs=1e-6),
    approx(-0.0030700573697686195, abs=1e-6),
    approx(0.053666338324546814, abs=1e-6),
    approx(0.06094931811094284, abs=1e-6),
    approx(0.0383155420422554, abs=1e-6),
    approx(-0.038075681775808334, abs=1e-6),
    approx(-0.026754574850201607, abs=1e-6),
    approx(0.0674038752913475, abs=1e-6),
    approx(-0.05417698994278908, abs=1e-6),
    approx(0.04589824005961418, abs=1e-6),
    approx(-0.012908651493489742, abs=1e-6),
    approx(0.015068471431732178, abs=1e-6),
    approx(0.0330093614757061, abs=1e-6),
    approx(0.004980822093784809, abs=1e-6),
    approx(0.043408624827861786, abs=1e-6),
    approx(0.06518962979316711, abs=1e-6),
    approx(0.01621568575501442, abs=1e-6),
    approx(0.03613711893558502, abs=1e-6),
    approx(0.055674269795417786, abs=1e-6),
    approx(0.11904116719961166, abs=1e-6),
    approx(0.024056661874055862, abs=1e-6),
    approx(0.015141709707677364, abs=1e-6),
    approx(0.06056377291679382, abs=1e-6),
    approx(0.036819666624069214, abs=1e-6),
    approx(0.04125634953379631, abs=1e-6),
    approx(-0.031558968126773834, abs=1e-6),
    approx(0.05625437572598457, abs=1e-6),
    approx(-0.02575598657131195, abs=1e-6),
    approx(0.01332936342805624, abs=1e-6),
    approx(0.04844079166650772, abs=1e-6),
    approx(-0.021774716675281525, abs=1e-6),
    approx(0.11008108407258987, abs=1e-6),
    approx(0.038589540868997574, abs=1e-6),
    approx(0.038059163838624954, abs=1e-6),
    approx(-0.004871065728366375, abs=1e-6),
    approx(-0.043283287435770035, abs=1e-6),
    approx(-0.025374216958880424, abs=1e-6),
    approx(0.01601369120180607, abs=1e-6),
    approx(0.01833094097673893, abs=1e-6),
    approx(0.05638064444065094, abs=1e-6),
    approx(0.034615058451890945, abs=1e-6),
    approx(-0.02065986767411232, abs=1e-6),
    approx(0.021277612075209618, abs=1e-6),
    approx(0.018937885761260986, abs=1e-6),
    approx(0.03383445739746094, abs=1e-6),
    approx(-0.00696248235180974, abs=1e-6),
    approx(-0.024325966835021973, abs=1e-6),
    approx(-0.019780263304710388, abs=1e-6),
    approx(-0.019895320758223534, abs=1e-6),
    approx(0.034644052386283875, abs=1e-6),
    approx(0.049627237021923065, abs=1e-6),
    approx(-0.029213037341833115, abs=1e-6),
]

huggingface_sequence_classification_with_raw_logits_expected_output = [
    {
        0: approx(-2.152204, abs=0.000009),
        1: approx(2.5094059, abs=0.000009),
    }
]
